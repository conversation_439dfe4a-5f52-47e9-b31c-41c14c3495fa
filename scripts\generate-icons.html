<!DOCTYPE html>
<html>
<head>
    <title>Generate PWA Icons</title>
</head>
<body>
    <h1>PWA Icon Generator</h1>
    <p>This will generate placeholder icons for the PWA.</p>
    <button onclick="generateIcons()">Generate Icons</button>
    <div id="output"></div>

    <script>
        function generateIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            const output = document.getElementById('output');
            output.innerHTML = '<h2>Generated Icons:</h2>';

            sizes.forEach(size => {
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                const ctx = canvas.getContext('2d');

                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#4f46e5'); // Primary color
                gradient.addColorStop(1, '#7c3aed'); // Secondary color
                
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);

                // Add text
                ctx.fillStyle = 'white';
                ctx.font = `bold ${size * 0.3}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('M', size / 2, size / 2);

                // Convert to blob and create download link
                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `icon-${size}x${size}.png`;
                    link.textContent = `Download icon-${size}x${size}.png`;
                    link.style.display = 'block';
                    link.style.margin = '5px 0';
                    output.appendChild(link);
                });
            });
        }
    </script>
</body>
</html>
