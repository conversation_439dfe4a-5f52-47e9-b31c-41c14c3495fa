/* Responsive Layout Styles */

/* CSS Custom Properties for responsive calculations */
:root {
  --sidebar-width: 256px;
  --viewport-width: 100vw;
  --viewport-height: 100vh;
  --is-mobile: 0;
  --is-tablet: 0;
  --is-desktop: 1;
  --header-height: 64px;
  --mobile-header-height: 56px;
}

/* Base responsive container */
.responsive-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  position: relative;
}

/* Responsive grid system */
.responsive-grid {
  display: grid;
  width: 100%;
  gap: 1rem;
  transition: all 0.3s ease-in-out;
}

/* Auto-adjusting grid columns */
.responsive-grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Responsive card layouts */
.responsive-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.responsive-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Responsive table wrapper */
.responsive-table-wrapper {
  width: 100%;
  overflow-x: auto;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background: white;
}

.responsive-table-wrapper table {
  width: 100%;
  border-collapse: collapse;
}

.responsive-table-wrapper th,
.responsive-table-wrapper td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #f3f4f6;
  white-space: nowrap;
}

.responsive-table-wrapper th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

/* Mobile table adjustments */
@media (max-width: 768px) {
  .responsive-table-wrapper {
    margin: 0 -0.5rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .responsive-table-wrapper th,
  .responsive-table-wrapper td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
  
  .responsive-table-wrapper th:first-child,
  .responsive-table-wrapper td:first-child {
    padding-left: 1rem;
  }
  
  .responsive-table-wrapper th:last-child,
  .responsive-table-wrapper td:last-child {
    padding-right: 1rem;
  }
}

/* Responsive sidebar */
.responsive-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  background: white;
  border-right: 1px solid #e5e7eb;
  z-index: 30;
  transition: all 0.3s ease-in-out;
  overflow-y: auto;
  overflow-x: hidden;
}

.responsive-sidebar.collapsed {
  transform: translateX(-100%);
}

.responsive-sidebar.mobile-hidden {
  transform: translateX(-100%);
}

@media (min-width: 1024px) {
  .responsive-sidebar.mobile-hidden {
    transform: translateX(0);
  }
}

/* Main content area adjustments */
.responsive-main-content {
  min-height: 100vh;
  transition: all 0.3s ease-in-out;
  margin-left: var(--sidebar-width);
}

.responsive-main-content.sidebar-collapsed {
  margin-left: 0;
}

@media (max-width: 1024px) {
  .responsive-main-content {
    margin-left: 0;
  }
}

/* Responsive modal adjustments */
.responsive-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.responsive-modal-content {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 32rem;
  max-height: calc(100vh - 2rem);
  overflow-y: auto;
  transition: all 0.3s ease-in-out;
}

@media (max-width: 640px) {
  .responsive-modal {
    padding: 0.5rem;
  }
  
  .responsive-modal-content {
    max-width: 100%;
    max-height: calc(100vh - 1rem);
    border-radius: 0.25rem;
  }
}

/* Responsive dropdown adjustments */
.responsive-dropdown {
  position: absolute;
  z-index: 40;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  min-width: 12rem;
  max-width: calc(100vw - 2rem);
  overflow: hidden;
}

@media (max-width: 640px) {
  .responsive-dropdown {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0.5rem 0.5rem 0 0;
    max-width: 100%;
    min-width: 100%;
  }
}

/* Responsive form layouts */
.responsive-form {
  display: grid;
  gap: 1rem;
  width: 100%;
}

.responsive-form-row {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-form-row.two-cols {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 768px) {
  .responsive-form-row.three-cols {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

/* Responsive button groups */
.responsive-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

@media (max-width: 640px) {
  .responsive-button-group {
    flex-direction: column;
    width: 100%;
  }
  
  .responsive-button-group button {
    width: 100%;
  }
}

/* Responsive navigation */
.responsive-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  height: var(--header-height);
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

@media (max-width: 768px) {
  .responsive-nav {
    height: var(--mobile-header-height);
    padding: 0 0.5rem;
  }
}

/* Responsive dashboard grid */
.responsive-dashboard-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Responsive stat cards */
.responsive-stat-card {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease-in-out;
}

@media (max-width: 640px) {
  .responsive-stat-card {
    padding: 1rem;
  }
}

/* Responsive check-in interface */
.responsive-checkin-layout {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
  height: calc(100vh - var(--header-height));
}

@media (min-width: 1024px) {
  .responsive-checkin-layout {
    grid-template-columns: 1fr 2fr;
  }
}

@media (max-width: 768px) {
  .responsive-checkin-layout {
    height: calc(100vh - var(--mobile-header-height));
    gap: 1rem;
  }
}

/* Utility classes for responsive behavior */
.hide-mobile {
  display: block;
}

@media (max-width: 768px) {
  .hide-mobile {
    display: none;
  }
}

.show-mobile {
  display: none;
}

@media (max-width: 768px) {
  .show-mobile {
    display: block;
  }
}

.hide-tablet {
  display: block;
}

@media (min-width: 768px) and (max-width: 1024px) {
  .hide-tablet {
    display: none;
  }
}

.show-tablet {
  display: none;
}

@media (min-width: 768px) and (max-width: 1024px) {
  .show-tablet {
    display: block;
  }
}

/* Smooth transitions for all responsive elements */
* {
  transition-property: margin, padding, width, height, transform, opacity;
  transition-duration: 0.3s;
  transition-timing-function: ease-in-out;
}

/* Disable transitions during resize to prevent janky animations */
.resizing * {
  transition: none !important;
}
