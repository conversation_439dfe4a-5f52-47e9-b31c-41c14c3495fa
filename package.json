{"name": "gym-management-app", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "vite build && npx serve dist", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/react": "^9.29.0", "@sentry/vite-plugin": "^3.5.0", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.38.4", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "framer-motion": "^10.16.4", "lucide-react": "^0.522.0", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-router-dom": "^7.6.1", "recharts": "^2.10.3", "stripe": "^18.2.1", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^20.8.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.31", "rollup-plugin-visualizer": "^6.0.3", "supabase": "^2.23.4", "tailwindcss": "^3.3.3"}}