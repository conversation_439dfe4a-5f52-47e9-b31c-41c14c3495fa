# Momentum Gym Management System - Environment Configuration
# Copy this file to .env and update the values for your environment

# ==================== SUPABASE CONFIGURATION ====================
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# ==================== APPLICATION CONFIGURATION ====================
VITE_APP_NAME="Momentum Gym Management"
VITE_APP_VERSION="1.0.0"
VITE_APP_ENVIRONMENT=development

# ==================== SCANNER CONFIGURATION ====================
# Barcode/QR scanner settings for check-in functionality
VITE_SCANNER_MIN_LENGTH=6
VITE_SCANNER_MAX_LENGTH=12
VITE_SCANNER_TIMEOUT=100
VITE_SCANNER_DEBUG=false

# ==================== DATABASE CONFIGURATION ====================
# Database query and pagination settings
VITE_DB_DEFAULT_LIMIT=20
VITE_DB_MAX_LIMIT=100
VITE_DB_MIN_LIMIT=5
VITE_DB_QUERY_TIMEOUT=30000

# ==================== CACHE CONFIGURATION ====================
# Cache settings for performance optimization
VITE_CACHE_TTL=300000
VITE_CACHE_MAX_SIZE=100

# ==================== LOGGING CONFIGURATION ====================
# Logging levels: debug, info, warn, error
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE_LOGS=true
VITE_ENABLE_EXTERNAL_LOGGING=false
VITE_MAX_LOG_ENTRIES=1000

# Context-specific logging (true/false)
VITE_LOG_DATABASE=true
VITE_LOG_AUTH=true
VITE_LOG_API=true
VITE_LOG_UI=false

# ==================== STRIPE CONFIGURATION ====================
# Payment processing (if using Stripe)
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key

# ==================== EXTERNAL SERVICES ====================
# SMS Service (if implementing SMS notifications)
VITE_SMS_SERVICE_API_KEY=your_sms_service_key
VITE_SMS_SERVICE_URL=your_sms_service_url

# Email Service (if implementing email notifications)
VITE_EMAIL_SERVICE_API_KEY=your_email_service_key
VITE_EMAIL_SERVICE_URL=your_email_service_url

# ==================== SECURITY CONFIGURATION ====================
# Session and security settings
VITE_SESSION_TIMEOUT=1800000
VITE_ENABLE_2FA=false
VITE_PASSWORD_MIN_LENGTH=8

# ==================== FEATURE FLAGS ====================
# Enable/disable specific features
VITE_ENABLE_FAMILY_MANAGEMENT=true
VITE_ENABLE_CORPORATE_DISCOUNTS=true
VITE_ENABLE_MULTI_LOCATION=true
VITE_ENABLE_ADVANCED_ANALYTICS=true
VITE_ENABLE_MOBILE_APP=false

# ==================== DEVELOPMENT CONFIGURATION ====================
# Development-specific settings
VITE_ENABLE_DEV_TOOLS=true
VITE_MOCK_DATA=false
VITE_BYPASS_AUTH=false

# ==================== PRODUCTION CONFIGURATION ====================
# Production-specific settings (set these in production)
# VITE_LOG_LEVEL=error
# VITE_ENABLE_CONSOLE_LOGS=false
# VITE_ENABLE_EXTERNAL_LOGGING=true
# VITE_SCANNER_DEBUG=false
