<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Permissions Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .two-panel {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            height: 600px;
        }
        .left-panel, .right-panel {
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 20px;
        }
        .left-panel {
            background-color: #f9f9f9;
        }
        .plan-item {
            padding: 12px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .plan-item:hover {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .plan-item.selected {
            background-color: #e3f2fd;
            border-color: #2196f3;
            box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
        }
        .permission-category {
            margin-bottom: 20px;
        }
        .permission-item {
            padding: 8px 12px;
            margin: 5px 0;
            border: 1px solid #eee;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .permission-item input[type="checkbox"] {
            margin: 0;
        }
        .save-btn {
            background-color: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 20px;
        }
        .save-btn:hover {
            background-color: #1976d2;
        }
        h3 {
            margin-top: 0;
            color: #333;
        }
        .category-header {
            font-weight: bold;
            color: #555;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Staff Plans & Permissions (UI Preview)</h1>
        <p>This demonstrates the two-panel UI layout for managing staff permissions.</p>
        
        <div class="two-panel">
            <!-- Left Panel - Staff Plans -->
            <div class="left-panel">
                <h3>📋 Staff Plans (3)</h3>
                <div class="plan-item selected" onclick="selectPlan(this, 'manager')">
                    <strong>🎯 Manager</strong>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        Full administrative access and oversight
                    </div>
                    <div style="font-size: 11px; color: #888; margin-top: 5px;">
                        <span style="background: #e3f2fd; padding: 2px 6px; border-radius: 3px;">12 / 15 permissions</span>
                    </div>
                </div>
                
                <div class="plan-item" onclick="selectPlan(this, 'trainer')">
                    <strong>💪 Personal Trainer</strong>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        Manages classes and member training
                    </div>
                    <div style="font-size: 11px; color: #888; margin-top: 5px;">
                        <span style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">8 / 15 permissions</span>
                    </div>
                </div>
                
                <div class="plan-item" onclick="selectPlan(this, 'desk')">
                    <strong>🏢 Front Desk</strong>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        Member check-in and basic support
                    </div>
                    <div style="font-size: 11px; color: #888; margin-top: 5px;">
                        <span style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px;">5 / 15 permissions</span>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel - Permissions -->
            <div class="right-panel">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>⚙️ Manager Permissions</h3>
                    <button class="save-btn">💾 Save Permissions</button>
                </div>
                
                <div class="permission-category">
                    <div class="category-header">
                        MEMBER MANAGEMENT
                        <span style="float: right; font-size: 11px;">
                            <a href="#" style="text-decoration: none;">Select All</a>
                        </span>
                    </div>
                    <div class="permission-item">
                        <input type="checkbox" checked>
                        <div>
                            <strong>Manage Members</strong>
                            <div style="font-size: 12px; color: #666;">Create, edit, and delete member profiles</div>
                        </div>
                    </div>
                    <div class="permission-item">
                        <input type="checkbox" checked>
                        <div>
                            <strong>View Members</strong>
                            <div style="font-size: 12px; color: #666;">View member profiles and information</div>
                        </div>
                    </div>
                    <div class="permission-item">
                        <input type="checkbox" checked>
                        <div>
                            <strong>Member Check-in</strong>
                            <div style="font-size: 12px; color: #666;">Check members in and out of the facility</div>
                        </div>
                    </div>
                </div>
                
                <div class="permission-category">
                    <div class="category-header">
                        CLASS MANAGEMENT
                        <span style="float: right; font-size: 11px;">
                            <a href="#" style="text-decoration: none;">Select All</a>
                        </span>
                    </div>
                    <div class="permission-item">
                        <input type="checkbox" checked>
                        <div>
                            <strong>Manage Classes</strong>
                            <div style="font-size: 12px; color: #666;">Create, edit, and delete fitness classes</div>
                        </div>
                    </div>
                    <div class="permission-item">
                        <input type="checkbox">
                        <div>
                            <strong>View Classes</strong>
                            <div style="font-size: 12px; color: #666;">View class schedules and information</div>
                        </div>
                    </div>
                </div>
                
                <div class="permission-category">
                    <div class="category-header">
                        FINANCIAL MANAGEMENT
                        <span style="float: right; font-size: 11px;">
                            <a href="#" style="text-decoration: none;">Deselect All</a>
                        </span>
                    </div>
                    <div class="permission-item">
                        <input type="checkbox" checked>
                        <div>
                            <strong>View Financial Reports</strong>
                            <div style="font-size: 12px; color: #666;">Access revenue and financial analytics</div>
                        </div>
                    </div>
                    <div class="permission-item">
                        <input type="checkbox" checked>
                        <div>
                            <strong>Manage Billing</strong>
                            <div style="font-size: 12px; color: #666;">Process payments and billing issues</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 6px; border-left: 4px solid #2196f3;">
            <h4 style="margin: 0 0 10px 0;">💡 How Staff Plan Permissions Work</h4>
            <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
                <li>Each staff plan represents a role type (e.g., Front Desk, Trainer, Manager)</li>
                <li>Select a plan from the left to view and modify its permissions</li>
                <li>Toggle permissions individually or use "Select All" for entire categories</li>
                <li>Click "Save Permissions" or press Ctrl+S to apply changes</li>
                <li>Staff members assigned to these plans inherit the permissions automatically</li>
            </ul>
        </div>
    </div>
    
    <script>
        function selectPlan(element, planType) {
            // Remove selected class from all plans
            document.querySelectorAll('.plan-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Add selected class to clicked plan
            element.classList.add('selected');
            
            // Update the right panel header
            const planNames = {
                'manager': 'Manager',
                'trainer': 'Personal Trainer', 
                'desk': 'Front Desk'
            };
            
            document.querySelector('.right-panel h3').textContent = `⚙️ ${planNames[planType]} Permissions`;
        }
    </script>
</body>
</html>
