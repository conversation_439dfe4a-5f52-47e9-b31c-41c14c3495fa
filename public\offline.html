<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Momentum Gym</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .offline-container {
            max-width: 400px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }

        .offline-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            color: white;
        }

        .offline-message {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .features-list {
            margin-top: 30px;
            text-align: left;
        }

        .features-list h3 {
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
            opacity: 0.8;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            font-size: 14px;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-offline {
            background: #ff6b6b;
        }

        .status-online {
            background: #51cf66;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @media (max-width: 480px) {
            .offline-container {
                padding: 30px 20px;
            }
            
            .offline-title {
                font-size: 24px;
            }
            
            .offline-message {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">
            📡
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            No internet connection detected. Some features may be limited, but you can still access cached content.
        </p>
        
        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="connection-status">
            <span class="status-indicator status-offline"></span>
            <span id="connection-text">Checking connection...</span>
        </div>
        
        <div class="features-list">
            <h3>Available Offline:</h3>
            <div class="feature-item">
                <div class="feature-icon">📋</div>
                <span>View cached member data</span>
            </div>
            <div class="feature-item">
                <div class="feature-icon">📅</div>
                <span>Browse class schedules</span>
            </div>
            <div class="feature-item">
                <div class="feature-icon">📊</div>
                <span>Review recent activity</span>
            </div>
            <div class="feature-item">
                <div class="feature-icon">⚙️</div>
                <span>Access app settings</span>
            </div>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusIndicator = document.querySelector('.status-indicator');
            const connectionText = document.getElementById('connection-text');
            
            if (navigator.onLine) {
                statusIndicator.className = 'status-indicator status-online';
                connectionText.textContent = 'Connection restored!';
                
                // Auto-reload after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                connectionText.textContent = 'No internet connection';
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(() => {
            // Try to fetch a small resource to verify connection
            fetch('/manifest.json', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                if (!navigator.onLine) {
                    // Connection is actually available
                    window.location.reload();
                }
            })
            .catch(() => {
                // Still offline
            });
        }, 5000);

        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready');
            });
        }
    </script>
</body>
</html>
