
import React from 'react';
import { generateUUID } from '@/lib/utils';
import { initialStaffRolesData } from './staffRolesData';
import { initialMembershipTypesData } from './membershipTypesData';
import { 
    initialGeneralSettingsData, 
    initialNotificationSettingsData, 
    initialSecuritySettingsData, 
    initialBillingSettingsData, 
    initialAdminPanelSettingsData, 
    initialReportingSettingsData, 
    initialAppearanceSettingsData 
} from './settingsData';
import { initialClassesData } from './classesData';
import { initialStaffMembersData } from './staffMembersData';
import { initialMembersData } from './membersData';
import { initialRoomsData } from './roomsData';
import { initialAttendanceData } from './attendanceData';
import { initialUserNotificationsData } from './userNotificationsData';
import { initialSupportTicketsData } from './supportTicketsData';
import { initialMemberNotesData } from './memberNotesData';

export const initialStaffRoles = initialStaffRolesData;
export const initialMembershipTypes = initialMembershipTypesData;
export const initialGeneralSettings = initialGeneralSettingsData;
export const initialNotificationSettings = initialNotificationSettingsData;
export const initialSecuritySettings = initialSecuritySettingsData;
export const initialBillingSettings = initialBillingSettingsData;
export const initialAdminPanelSettings = initialAdminPanelSettingsData;
export const initialReportingSettings = initialReportingSettingsData;
export const initialAppearanceSettings = initialAppearanceSettingsData;
export const initialClasses = initialClassesData;
export const initialStaffMembers = initialStaffMembersData;
export const initialMembers = initialMembersData;
export const initialRooms = initialRoomsData;
export const initialAttendance = initialAttendanceData;
export const initialUserNotifications = initialUserNotificationsData;
export const initialSupportTickets = initialSupportTicketsData;
export const initialMemberNotes = initialMemberNotesData;


export const initialDataSets = {
  initialStaffRoles,
  initialMembershipTypes,
  initialGeneralSettings,
  initialNotificationSettings,
  initialSecuritySettings,
  initialBillingSettings,
  initialAdminPanelSettings,
  initialReportingSettings,
  initialAppearanceSettings,
  initialClasses,
  initialStaffMembers,
  initialMembers,
  initialRooms,
  initialAttendance,
  initialUserNotifications,
  initialSupportTickets,
  initialMemberNotes,
};



